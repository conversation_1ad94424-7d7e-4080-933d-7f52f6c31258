"use client";

import { useRef } from 'react';
import { useScroll } from 'framer-motion';
import SectionTitleAnimation from './SectionTitleAnimation';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);

  // Use Framer Motion's useScroll for native scroll tracking (no smoothing)
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
    layoutEffect: false // Disable layout effects that might cause smoothing
  });

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  // Use a fixed section height that gives enough scroll distance
  const sectionHeight = '450vh'; // Increased to ensure last card has time to reach center

  return (
    <>
      {/* Reusable Section Title Animation */}
      <SectionTitleAnimation
        title="Process"
        currentSectionRef={sectionRef}
        previousSectionSelector='[data-section="projects"]'
        zIndex="z-0"
        className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
      />

      {/* Process Section - Dynamic height calculated to stop when last card is centered */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: sectionHeight }} // Fixed height to ensure last card reaches center
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
              scrollProgress={scrollYProgress}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
